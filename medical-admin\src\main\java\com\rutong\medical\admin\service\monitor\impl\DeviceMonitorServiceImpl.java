package com.rutong.medical.admin.service.monitor.impl;

import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import cn.hutool.core.collection.CollUtil;
import com.rutong.medical.admin.entity.station.DeviceBaseStation;
import com.rutong.medical.admin.entity.station.DeviceBaseStationMonitor;
import com.rutong.medical.admin.mapper.station.DeviceBaseStationMapper;
import com.rutong.medical.admin.mapper.station.DeviceBaseStationMonitorMapper;
import com.rutong.medical.admin.service.station.DeviceBaseStationService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import com.alibaba.excel.EasyExcelFactory;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageHelper;
import com.rutong.medical.admin.constant.MonitorDeviceTypeEnum;
import com.rutong.medical.admin.constant.MonitorTypeEnum;
import com.rutong.medical.admin.dto.monitor.DeviceMonitorQueryDTO;
import com.rutong.medical.admin.dto.monitor.DeviceMonitorSaveDTO;
import com.rutong.medical.admin.dto.monitor.EquipmentControlParamDTO;
import com.rutong.medical.admin.entity.monitor.DeviceMonitor;
import com.rutong.medical.admin.entity.system.Space;
import com.rutong.medical.admin.mapper.monitor.DeviceMonitorMapper;
import com.rutong.medical.admin.mapper.system.SpaceMapper;
import com.rutong.medical.admin.service.monitor.DeviceMonitorService;
import com.rutong.medical.admin.vo.monitor.DeviceMonitorImportVO;
import com.rutong.medical.admin.vo.monitor.DeviceMonitorVO;
import com.rutong.medical.admin.vo.monitor.EquipmentImportCheckVO;
import com.soft.common.core.constant.GlobalDeletedFlag;
import com.soft.common.core.exception.MyRuntimeException;
import com.soft.common.core.exception.ServiceException;
import com.soft.common.core.http.HttpClientUtil;
import com.soft.common.core.object.MyPageData;
import com.soft.common.core.object.TokenData;
import com.soft.common.core.util.ExcelSelectOptions;
import com.soft.common.core.util.ExcelUtils;
import com.soft.common.core.util.MyModelUtil;
import com.soft.common.core.util.MyPageUtil;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * 视频监控Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-07-14
 */
@Service
@Slf4j
public class DeviceMonitorServiceImpl extends ServiceImpl<DeviceMonitorMapper, DeviceMonitor>
        implements DeviceMonitorService {

    @Autowired
    private DeviceMonitorMapper deviceMonitorMapper;
    @Resource
    private DeviceMonitorService deviceMonitorService;
    @Resource
    private SpaceMapper spaceMapper;
    @Resource
    private DeviceBaseStationMonitorMapper deviceBaseStationMonitorMapper;
    @Resource
    private DeviceBaseStationMapper deviceBaseStationMapper;

    @Value("${mediaService.ip}")
    private String ip;

    @Value("${mediaService.secret}")
    private String secret;

    /**
     * 是否主码流
     */
    private static final boolean mainStream = true;

    @Override
    public List<DeviceMonitorVO> listMonitor(DeviceMonitorQueryDTO deviceMonitorQueryDTO) {
        List<DeviceMonitor> deviceMonitorList = deviceMonitorMapper.list(deviceMonitorQueryDTO);
        if (CollectionUtils.isEmpty(deviceMonitorList)) {
            return Collections.emptyList();
        }
        return MyModelUtil.copyCollectionTo(deviceMonitorList, DeviceMonitorVO.class);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delete(Long id) {
        DeviceMonitor deviceMonitor = deviceMonitorMapper.selectById(id);
        if (deviceMonitor == null) {
            throw new ServiceException("监控设备不存在");
        }

        deviceMonitor.setUpdateTime(new Date());
        deviceMonitor.setUpdateUserId(TokenData.takeFromRequest().getUserId());
        deviceMonitor.setIsDelete(GlobalDeletedFlag.DELETED);
        deviceMonitorMapper.updateById(deviceMonitor);
    }

    @Override
    public DeviceMonitorVO detail(Long id) {
        DeviceMonitor deviceMonitor = deviceMonitorMapper.selectById(id);
        if (deviceMonitor == null) {
            throw new ServiceException("监控设备不存在");
        }
        return MyModelUtil.copyTo(deviceMonitor, DeviceMonitorVO.class);
    }

    @Override
    public MyPageData<DeviceMonitorVO> page(DeviceMonitorQueryDTO deviceMonitorQueryDTO) {
        Integer pageNum = deviceMonitorQueryDTO.getPageNum();
        Integer pageSize = deviceMonitorQueryDTO.getPageSize();
        if (pageNum != null && pageSize != null) {
            PageHelper.startPage(pageNum, pageSize);
        }
        List<DeviceMonitor> deviceMonitorList = deviceMonitorMapper.list(deviceMonitorQueryDTO);
        if (CollectionUtils.isEmpty(deviceMonitorList)) {
            return MyPageData.emptyPageData();
        }

        MyPageData<DeviceMonitorVO> deviceBaseStationVOMyPageData =
                MyPageUtil.makeResponseData(deviceMonitorList, DeviceMonitor.INSTANCE);
        List<DeviceMonitorVO> dataList = deviceBaseStationVOMyPageData.getDataList();
        if (CollectionUtils.isEmpty(dataList)) {
            return MyPageData.emptyPageData();
        }
        return deviceBaseStationVOMyPageData;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveOrUpdate(DeviceMonitorSaveDTO deviceMonitorSaveDTO) {
        Long id = deviceMonitorSaveDTO.getId();
        Long spaceId = deviceMonitorSaveDTO.getSpaceId();
        if (id == null) {
            // 1. 插入 MySQL
            if (deviceMonitorMapper.existsByDeviceNumberOrName(deviceMonitorSaveDTO.getMonitorCode(),
                    deviceMonitorSaveDTO.getMonitorName()) > 0) {
                throw new ServiceException("视频编号或视频名称已存在");
            }
            DeviceMonitor deviceMonitor = MyModelUtil.copyTo(deviceMonitorSaveDTO, DeviceMonitor.class);
            deviceMonitor.setCreateTime(new Date());
            deviceMonitor.setIsDelete(GlobalDeletedFlag.NORMAL);
            deviceMonitor.setCreateUserId(TokenData.takeFromRequest().getUserId());

            // 查询位置
            if (spaceId != null) {
                Space space = spaceMapper.selectById(spaceId);
                if (space != null) {
                    deviceMonitor.setSpacePath(space.getPath());
                    deviceMonitor.setSpaceFullName(space.getFullName());
                }
            }
            deviceMonitorMapper.insert(deviceMonitor);
        } else {
            int i = deviceMonitorMapper.existsByDeviceNumberOrNameExcludingId(deviceMonitorSaveDTO.getMonitorCode(),
                    deviceMonitorSaveDTO.getMonitorName(), deviceMonitorSaveDTO.getId());
            if (i > 0) {
                throw new ServiceException("视频编号视频名称已存在");
            }
            DeviceMonitor deviceMonitor = MyModelUtil.copyTo(deviceMonitorSaveDTO, DeviceMonitor.class);
            deviceMonitor.setUpdateUserId(TokenData.takeFromRequest().getUserId());
            deviceMonitor.setUpdateTime(new Date());
            // 查询位置
            if (spaceId != null) {
                Space space = spaceMapper.selectById(spaceId);
                if (space != null) {
                    deviceMonitor.setSpacePath(space.getPath());
                    deviceMonitor.setSpaceFullName(space.getFullName());
                }
            } else {
                deviceMonitor.setSpacePath(null);
                deviceMonitor.setSpaceFullName(null);
            }
            deviceMonitorMapper.updateById(deviceMonitor);
        }
    }

    @Override
    public void exportTemplate() {
        ExcelUtils.ExcelTitle excelTitle = new ExcelUtils.ExcelTitle();
        ExcelSelectOptions excelSelectOptions = ExcelSelectOptions.createSelectOptions();
        List<String> monitorNames = MonitorTypeEnum.getAllNamesAsList();
        excelSelectOptions.put(2, monitorNames.toArray(new String[0]));
        List<String> allNames = MonitorDeviceTypeEnum.getAllNames();
        excelSelectOptions.put(8, allNames.toArray(new String[0]));
        ExcelUtils.exportPresetHandler(DeviceMonitorImportVO.class, null, "视频监控导入模板", "sheet", excelTitle,
                excelSelectOptions);

    }

    @Override
    public Map<String, String> getFactory() {
        return MonitorDeviceTypeEnum.getCodeNameMap();
    }

    @Override
    public void importExcel(EquipmentImportCheckVO equipmentImportCheckVO) throws IOException {
        // 获取Excel文件的数据
        List<DeviceMonitor> insertEquipments = equipmentImportCheckVO.getInsertEquipments();
        if (CollectionUtils.isNotEmpty(insertEquipments)) {
            deviceMonitorService.saveBatch(insertEquipments);
        }

        List<DeviceMonitor> updateEquipments = equipmentImportCheckVO.getUpdateEquipments();
        if (CollectionUtils.isNotEmpty(updateEquipments)) {
            deviceMonitorMapper.batchUpdate(updateEquipments);
        }
    }

    @Override
    public EquipmentImportCheckVO importCheck(MultipartFile file) throws IOException {
        List<DeviceMonitorImportVO> excelDTOList =
                EasyExcelFactory.read(file.getInputStream()).head(DeviceMonitorImportVO.class).sheet().doReadSync();

        EquipmentImportCheckVO equipmentImportCheckVO = new EquipmentImportCheckVO();
        if (CollectionUtil.isEmpty(excelDTOList)) {
            return equipmentImportCheckVO;
        }

        Set<String> equipmentNos = new HashSet<>();

        excelDTOList.forEach(equipmentImportTemplateVO -> {
            equipmentNos.add(equipmentImportTemplateVO.getMonitorCode());
        });

        // 查询设备
        Map<String, DeviceMonitor> equipmentMap = new HashMap<>();
        if (CollectionUtil.isNotEmpty(equipmentNos)) {
            List<DeviceMonitor> equipments = deviceMonitorMapper
                    .selectList(Wrappers.lambdaQuery(DeviceMonitor.class).in(DeviceMonitor::getMonitorCode, equipmentNos));
            if (CollectionUtil.isNotEmpty(equipments)) {
                equipmentMap = equipments.stream()
                        .collect(Collectors.toMap(DeviceMonitor::getMonitorCode, equipment -> equipment));
            }
        }

        // 返回信息
        List<DeviceMonitor> insertEquipments = new ArrayList<>();
        List<DeviceMonitor> updateEquipments = new ArrayList<>();
        List<EquipmentImportCheckVO.ErrorMessage> errorMessages = new ArrayList<>();
        Date date = new Date();
        Long userId = TokenData.takeFromRequest().getUserId();
        for (int i = 0; i < excelDTOList.size(); i++) {
            DeviceMonitorImportVO equipmentImportTemplateVO = excelDTOList.get(i);
            String equipmentNo = equipmentImportTemplateVO.getMonitorCode();
            String equipmentName = equipmentImportTemplateVO.getMonitorName();

            String ip = equipmentImportTemplateVO.getIp();
            Long port = equipmentImportTemplateVO.getPort();
            String channelNum = equipmentImportTemplateVO.getChannelNum();
            String userCode = equipmentImportTemplateVO.getUserCode();
            String password = equipmentImportTemplateVO.getPassword();
            String factory = equipmentImportTemplateVO.getFactory();

            String equipmentTypeStr = equipmentImportTemplateVO.getMonitorType();

            DeviceMonitor equipment = new DeviceMonitor();

            // 异常错误对象
            EquipmentImportCheckVO.ErrorMessage errorMessage = new EquipmentImportCheckVO.ErrorMessage();
            errorMessage.setRowNo(i + 3);
            errorMessage.setEquipmentExcelTemplateVO(equipmentImportTemplateVO);
            List<String> errorMessagesStr = new ArrayList<>();
            errorMessage.setErrorMessages(errorMessagesStr);

            // 设备名称
            if (StrUtil.isBlank(equipmentName)) {
                errorMessagesStr.add("【设备名称】不能为空！");
            }

            if (StrUtil.isBlank(equipmentNo)) {
                errorMessagesStr.add("【设备编号】不能为空！");
            }

            if (StrUtil.isBlank(ip)) {
                errorMessagesStr.add("【ip地址】不能为空！");
            }

            if (StrUtil.isBlank(channelNum)) {
                errorMessagesStr.add("【通道号】不能为空！");
            }

            if (StrUtil.isBlank(password)) {
                errorMessagesStr.add("【密码】不能为空！");
            }

            if (StrUtil.isBlank(userCode)) {
                errorMessagesStr.add("【用户名】不能为空！");
            }

            if (port == null) {
                errorMessagesStr.add("【端口】不能为空！");
            }

            // 对数据进行校验

            DeviceMonitor equipmentDB = equipmentMap.get(equipmentNo);

            // 有异常，跳出
            if (CollectionUtil.isNotEmpty(errorMessagesStr)) {
                errorMessages.add(errorMessage);
                continue;
            }

            equipment.setMonitorCode(equipmentNo);
            equipment.setMonitorName(equipmentName);
            equipment.setIp(ip);
            equipment.setPort(port);
            equipment.setChannelNum(channelNum);
            equipment.setPassword(password);
            Optional<String> codeFromName = MonitorDeviceTypeEnum.getCodeFromName(factory);
            if (codeFromName.isPresent()) {
                equipment.setFactory(codeFromName.get());
            }
            Integer codeByName = MonitorTypeEnum.getCodeByName(equipmentTypeStr);
            equipment.setMonitorType(codeByName);
            equipment.setUserCode(userCode);
            equipment.setCreateUserId(userId);
            if (equipmentDB != null) {
                equipment.setId(equipmentDB.getId());
                updateEquipments.add(equipment);
            } else {
                equipment.setIsDelete(GlobalDeletedFlag.NORMAL);
                equipment.setCreateTime(date);
                insertEquipments.add(equipment);
            }
        }
        equipmentImportCheckVO.setInsertEquipments(insertEquipments);
        equipmentImportCheckVO.setUpdateEquipments(updateEquipments);
        equipmentImportCheckVO.setErrorMessages(errorMessages);
        return equipmentImportCheckVO;
    }

    @Override
    public String playFlv(EquipmentControlParamDTO param) {
        Long equipmentId = param.getEquipmentId();
        DeviceMonitor deviceMonitor = deviceMonitorMapper.selectById(equipmentId);
        if (deviceMonitor == null) {
            throw new RuntimeException("设施不存在或已删除！");
        }
        // 获取对应厂商控制接口实现

        String rtspUrl = previewURLs(deviceMonitor);
        rtspUrl = "rtsp://admin:1234hzxr@*************:554/h264/ch11/main/av_stream";
        JSONObject paramObject = new JSONObject();
        paramObject.put("vhost", "__defaultVhost__");
        paramObject.put("app", "live");
        paramObject.put("stream", equipmentId);
        paramObject.put("url", rtspUrl);
        paramObject.put("secret", secret);
        String flvUrl = null;
        try {
            // log.info("通用监控设备 调用视频转码接口入参:{}", paramObject);
            String msg =
                    HttpClientUtil.httpSyncPost("http://" + ip + "/index/api/addStreamProxy", paramObject.toJSONString());
            JSONObject rsMsg = JSON.parseObject(msg);
            if (Integer.valueOf(String.valueOf(rsMsg.get("code"))) != 0) {
                log.info("视频转码失败:{}", rsMsg);
                throw new MyRuntimeException("视频转码失败！");
            }
            flvUrl = "http://" + ip + "/live/" + equipmentId + ".live.mp4";
        } catch (Exception e) {
            throw new MyRuntimeException("流媒体服务启动失败！");
        }
        return flvUrl;
    }

    @Override
    public List<String> playFlvByBaseStation(String baseStationSn) {
        List<String> list = new ArrayList<>();
        DeviceBaseStation deviceBaseStation = deviceBaseStationMapper.selectOne(
                Wrappers.lambdaQuery(DeviceBaseStation.class)
                        .eq(DeviceBaseStation::getDeviceBaseStationCode, baseStationSn));
        if (ObjectUtils.isEmpty(deviceBaseStation)) {
            return Collections.emptyList();
        }
        List<DeviceBaseStationMonitor> deviceBaseStationMonitors = deviceBaseStationMonitorMapper.selectList(
                Wrappers.lambdaQuery(DeviceBaseStationMonitor.class)
                        .eq(DeviceBaseStationMonitor::getDeviceBaseStationId, deviceBaseStation.getId()));
        if (CollUtil.isEmpty(deviceBaseStationMonitors)) {
            return Collections.emptyList();
        }
        for (DeviceBaseStationMonitor deviceBaseStationMonitor : deviceBaseStationMonitors) {
            EquipmentControlParamDTO equipmentControlParamDTO = new EquipmentControlParamDTO();
            equipmentControlParamDTO.setEquipmentId(deviceBaseStationMonitor.getDeviceMonitorId());
            String flvUrl = playFlv(equipmentControlParamDTO);
            list.add(flvUrl);
        }

        return list;
    }

    /**
     * 获取预览地址
     *
     * @param param
     * @return
     */
    public String previewURLs(DeviceMonitor param) {
        String factory = param.getFactory();
        switch (factory) {
            case "DA_HUA_MONITOR":
                // 固定主码流
                return buildDaHuaRTSP(param, false);
            case "DA_HUA_MONITOR_V5":
                return buildDaHuaV5RTSP(param, mainStream);
            case "HIK_MONITOR":
                return buildHikVisionRTSP(param, mainStream);
            case "YUSHI_MONITOR":
                return buildYuShiRTSP(param, mainStream);
            default:
                throw new IllegalArgumentException("不支持的设备厂商: " + factory);
        }
    }

    // 大华（旧版本）：固定主码流
    private String buildDaHuaRTSP(DeviceMonitor param, boolean isMainStream) {

        StringBuilder rtsp = new StringBuilder();
        rtsp.append("rtsp://").append(param.getUserCode()).append(":").append(param.getPassword()).append("@")
                .append(param.getIp()).append("/cam/realmonitor?").append("channel=").append(param.getChannelNum() + 1)
                .append("&subtype=0");
        return rtsp.toString();
    }

    // 大华 V5：支持主/副码流
    private String buildDaHuaV5RTSP(DeviceMonitor param, boolean isMainStream) {
        StringBuilder rtsp = new StringBuilder();
        rtsp.append("rtsp://").append(param.getUserCode()).append(":").append(param.getPassword()).append("@")
                .append(param.getIp()).append("/cam/realmonitor?").append("channel=").append(param.getChannelNum() + 1)
                .append("&subtype=").append(isMainStream ? "0" : "1");
        return rtsp.toString();
    }

    // 海康威视：特殊格式
    private String buildHikVisionRTSP(DeviceMonitor param, boolean isMainStream) {

        /* StringBuilder rtsp = new StringBuilder();
        rtsp.append("rtsp://").append(param.getUserCode()).append(":").append(param.getPassword()).append("@")
            .append(param.getIp()).append("/").append(object.getString("encoding")).append("/ch")
            .append(attribute2.getAttributeValue()).append(isMainStream? "/main/av_stream" : "/sub/av_stream");*/

        // return rtsp.toString();
        return "";
    }

    // 宇视：与大华 V5 类似
    private String buildYuShiRTSP(DeviceMonitor param, boolean isMainStream) {
        StringBuilder rtsp = new StringBuilder();
        rtsp.append("rtsp://").append(param.getUserCode()).append(":").append(param.getPassword()).append("@")
                .append(param.getIp()).append("/cam/realmonitor?").append("channel=").append(param.getChannelNum() + 1)
                .append("&subtype=").append(isMainStream ? "0" : "1");
        return rtsp.toString();
    }

}
